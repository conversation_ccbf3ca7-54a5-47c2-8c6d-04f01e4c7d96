import pandas as pd

# Load data
df = pd.read_csv("greater than 10 new_test_output_ads_v3.csv", index_col=0)

# Check timing columns
timing_cols = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']

print("Timing column unique values:")
for col in timing_cols:
    print(f"\n{col}:")
    print(df[col].value_counts(dropna=False))

# Convert to numeric
for col in timing_cols:
    df[col] = pd.to_numeric(df[col], errors='coerce')

print("\nAfter numeric conversion:")
for col in timing_cols:
    print(f"\n{col}:")
    print(df[col].value_counts(dropna=False))

# Check how many records have all zeros
all_zero = (
    (df['Same Week'] == 0) & 
    (df['1 wk before'] == 0) & 
    (df['2 wk before'] == 0) & 
    (df['1 wk after'] == 0) & 
    (df['2 wk after'] == 0)
)

print(f"\nRecords with all timing columns = 0: {all_zero.sum()}")
print(f"Total records: {len(df)}")
print(f"Percentage with all zeros: {all_zero.sum()/len(df)*100:.1f}%")
