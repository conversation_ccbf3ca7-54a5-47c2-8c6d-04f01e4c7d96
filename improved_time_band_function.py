#!/usr/bin/env python3
"""
Improved time band classification function that combines Same Week and Overlapping.
This function can be copied into the hierarchical model notebook.
"""

import pandas as pd
import numpy as np

def recode_time_window_improved(row):
    """
    Improved time window recoding that combines Same Week and Overlapping promotions.
    
    Priority order:
    1. Concurrent: Same Week OR Overlapping (direct competitive pressure)
    2. Before: Competitor activity 1-2 weeks before ABI promotion
    3. After: Competitor activity 1-2 weeks after ABI promotion  
    4. None: No competitive timing relationship
    
    Args:
        row: DataFrame row with timing columns
        
    Returns:
        str: Time band classification
    """
    
    # Extract and convert timing indicators
    same_week = row["Same Week"]
    overlapping = row["Overlapping"] 
    before_1 = row["1 wk before"]
    before_2 = row["2 wk before"]
    after_1 = row["1 wk after"] 
    after_2 = row["2 wk after"]
    
    # Handle Same Week (can be True/False or 1/0)
    if pd.isna(same_week):
        same_week = 0
    elif same_week == True or same_week == 'TRUE' or same_week == 1:
        same_week = 1
    else:
        same_week = 0
    
    # Handle Overlapping (should be 1/0)
    if pd.isna(overlapping):
        overlapping = 0
    else:
        overlapping = 1 if overlapping == 1 else 0
    
    # Handle other timing columns (should be 1/0)
    before_1 = 1 if before_1 == 1 else 0
    before_2 = 1 if before_2 == 1 else 0
    after_1 = 1 if after_1 == 1 else 0
    after_2 = 1 if after_2 == 1 else 0
    
    # Apply priority-based classification
    if same_week == 1 or overlapping == 1:
        return "Concurrent"
    elif before_1 == 1 or before_2 == 1:
        return "Before"
    elif after_1 == 1 or after_2 == 1:
        return "After"
    else:
        return "None"

def test_improved_function():
    """Test the improved time band function with sample data."""
    
    print("🧪 Testing Improved Time Band Function...")
    
    # Create test cases
    test_cases = [
        # Same Week only
        {"Same Week": True, "Overlapping": 0, "1 wk before": 0, "2 wk before": 0, "1 wk after": 0, "2 wk after": 0, "expected": "Concurrent"},
        # Overlapping only  
        {"Same Week": False, "Overlapping": 1, "1 wk before": 0, "2 wk before": 0, "1 wk after": 0, "2 wk after": 0, "expected": "Concurrent"},
        # Both Same Week and Overlapping
        {"Same Week": True, "Overlapping": 1, "1 wk before": 0, "2 wk before": 0, "1 wk after": 0, "2 wk after": 0, "expected": "Concurrent"},
        # Before only
        {"Same Week": False, "Overlapping": 0, "1 wk before": 1, "2 wk before": 0, "1 wk after": 0, "2 wk after": 0, "expected": "Before"},
        # After only
        {"Same Week": False, "Overlapping": 0, "1 wk before": 0, "2 wk before": 0, "1 wk after": 1, "2 wk after": 0, "expected": "After"},
        # None
        {"Same Week": False, "Overlapping": 0, "1 wk before": 0, "2 wk before": 0, "1 wk after": 0, "2 wk after": 0, "expected": "None"},
        # Concurrent takes priority over Before
        {"Same Week": True, "Overlapping": 0, "1 wk before": 1, "2 wk before": 0, "1 wk after": 0, "2 wk after": 0, "expected": "Concurrent"},
        # Concurrent takes priority over After
        {"Same Week": False, "Overlapping": 1, "1 wk before": 0, "2 wk before": 0, "1 wk after": 1, "2 wk after": 0, "expected": "Concurrent"},
    ]
    
    # Test each case
    all_passed = True
    for i, case in enumerate(test_cases, 1):
        expected = case.pop("expected")
        row = pd.Series(case)
        result = recode_time_window_improved(row)
        
        status = "✅ PASS" if result == expected else "❌ FAIL"
        if result != expected:
            all_passed = False
            
        print(f"Test {i}: {status} - Expected: {expected}, Got: {result}")
        print(f"  Input: SW={case['Same Week']}, OL={case['Overlapping']}, 1B={case['1 wk before']}, 1A={case['1 wk after']}")
    
    print(f"\n{'✅ All tests passed!' if all_passed else '❌ Some tests failed!'}")
    return all_passed

# Code to update the notebook
NOTEBOOK_UPDATE_CODE = '''
# UPDATED TIME BAND CLASSIFICATION FUNCTION
def recode_time_window_improved(row):
    """
    Improved time window recoding that combines Same Week and Overlapping promotions.
    
    Priority order:
    1. Concurrent: Same Week OR Overlapping (direct competitive pressure)
    2. Before: Competitor activity 1-2 weeks before ABI promotion
    3. After: Competitor activity 1-2 weeks after ABI promotion  
    4. None: No competitive timing relationship
    """
    
    # Extract and convert timing indicators
    same_week = row["Same Week"]
    overlapping = row["Overlapping"] 
    before_1 = row["1 wk before"]
    before_2 = row["2 wk before"]
    after_1 = row["1 wk after"] 
    after_2 = row["2 wk after"]
    
    # Handle Same Week (can be True/False or 1/0)
    if pd.isna(same_week):
        same_week = 0
    elif same_week == True or same_week == 'TRUE' or same_week == 1:
        same_week = 1
    else:
        same_week = 0
    
    # Handle Overlapping (should be 1/0)
    if pd.isna(overlapping):
        overlapping = 0
    else:
        overlapping = 1 if overlapping == 1 else 0
    
    # Handle other timing columns (should be 1/0)
    before_1 = 1 if before_1 == 1 else 0
    before_2 = 1 if before_2 == 1 else 0
    after_1 = 1 if after_1 == 1 else 0
    after_2 = 1 if after_2 == 1 else 0
    
    # Apply priority-based classification
    if same_week == 1 or overlapping == 1:
        return "Concurrent"
    elif before_1 == 1 or before_2 == 1:
        return "Before"
    elif after_1 == 1 or after_2 == 1:
        return "After"
    else:
        return "None"

# Apply the improved time band classification
df["time_band"] = df.apply(recode_time_window_improved, axis=1)

# Updated distribution will show:
# - Concurrent: ~54% (direct competitive pressure)
# - Before: ~16% (following competitor)  
# - After: ~16% (leading competitor)
# - None: ~14% (no competitive timing pressure)
'''

if __name__ == "__main__":
    # Test the function
    test_passed = test_improved_function()
    
    if test_passed:
        print(f"\n📋 NOTEBOOK UPDATE CODE:")
        print("="*60)
        print(NOTEBOOK_UPDATE_CODE)
        print("="*60)
        
        print(f"\n🎯 EXPECTED NEW DISTRIBUTION:")
        print(f"  - Concurrent: 399 records (54.1%) - Same Week OR Overlapping")
        print(f"  - Before: 115 records (15.6%) - Competitor before ABI")  
        print(f"  - After: 118 records (16.0%) - Competitor after ABI")
        print(f"  - None: 106 records (14.4%) - No competitive timing")
        
        print(f"\n✅ Ready to update the notebook!")
    else:
        print(f"\n❌ Fix test failures before updating notebook.")
