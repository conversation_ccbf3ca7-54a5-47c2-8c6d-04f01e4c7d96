#!/usr/bin/env python3
"""
Investigate why there are so many 'None' records in the time band classification.
This suggests an issue with the time window recoding logic.
"""

import pandas as pd
import numpy as np

def investigate_time_bands():
    print("🔍 Investigating Time Band 'None' Issue...")
    
    # Load the data
    DATA_PATH = "greater than 10 new_test_output_ads_v3.csv"
    
    try:
        df_raw = pd.read_csv(DATA_PATH, index_col=0)
        print(f"✅ Data loaded successfully: {df_raw.shape}")
    except FileNotFoundError:
        print(f"❌ Data file not found: {DATA_PATH}")
        return
    
    # Define required columns
    REQUIRED_COLUMNS = [
        'Retailer', 'ABI Mechanic', 'Same Week', '1 wk after', '2 wk after', 
        '1 wk before', '2 wk before', 'Avg Temp', 'ABI Coverage', 
        'ABI vs Segment PTC Index Agg', 'Competitor SKU', 'ABI Start', 'ABI End'
    ]
    
    # Select required columns
    df = df_raw[REQUIRED_COLUMNS].copy()
    
    # Convert timing columns to numeric
    timing_cols = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']
    
    print(f"\n📊 ORIGINAL TIMING COLUMN VALUES:")
    for col in timing_cols:
        print(f"\n{col}:")
        print(f"  Unique values: {sorted(df[col].unique())}")
        print(f"  Value counts:")
        print(df[col].value_counts(dropna=False).head(10))
    
    # Convert to numeric
    for col in timing_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    print(f"\n📊 AFTER NUMERIC CONVERSION:")
    for col in timing_cols:
        print(f"\n{col}:")
        print(f"  Unique values: {sorted(df[col].unique())}")
        print(f"  Value counts:")
        print(df[col].value_counts(dropna=False).head(10))
    
    # Apply the current recoding logic
    def recode_time_window_debug(row):
        same_week = row["Same Week"]
        before_1 = row["1 wk before"]
        before_2 = row["2 wk before"] 
        after_1 = row["1 wk after"]
        after_2 = row["2 wk after"]
        
        # Debug info
        debug_info = {
            'same_week_raw': same_week,
            'before_1': before_1,
            'before_2': before_2,
            'after_1': after_1,
            'after_2': after_2
        }
        
        # Handle same week logic
        if pd.isna(same_week):
            same_week = 0
        elif same_week == True or same_week == 'TRUE' or same_week == 1:
            same_week = 1
        else:
            same_week = 0
        
        debug_info['same_week_processed'] = same_week
        
        if same_week == 1:
            return "Same", debug_info
        elif before_1 == 1 or before_2 == 1:
            return "Before", debug_info
        elif after_1 == 1 or after_2 == 1:
            return "After", debug_info
        else:
            return "None", debug_info
    
    # Apply recoding with debug info
    results = df.apply(recode_time_window_debug, axis=1)
    df["time_band"] = [r[0] for r in results]
    debug_info = [r[1] for r in results]
    
    print(f"\n📊 TIME BAND RESULTS:")
    time_counts = df['time_band'].value_counts()
    print(time_counts)
    
    # Analyze the 'None' records
    none_mask = df['time_band'] == 'None'
    none_records = df[none_mask].copy()
    none_debug = [debug_info[i] for i in range(len(df)) if none_mask.iloc[i]]
    
    print(f"\n🔍 ANALYZING {len(none_records)} 'NONE' RECORDS:")
    
    # Check what values the None records have
    print(f"\nTiming values for 'None' records:")
    for col in timing_cols:
        print(f"\n{col} in None records:")
        none_values = none_records[col].value_counts(dropna=False)
        print(none_values.head(10))
    
    # Sample some None records for detailed inspection
    print(f"\n🔬 SAMPLE 'NONE' RECORDS (first 10):")
    sample_none = none_records.head(10)
    for idx, (_, row) in enumerate(sample_none.iterrows()):
        print(f"\nRecord {idx + 1}:")
        for col in timing_cols:
            print(f"  {col}: {row[col]} (type: {type(row[col])})")
        print(f"  Debug info: {none_debug[idx]}")
    
    # Check for patterns in None records
    print(f"\n📈 PATTERNS IN 'NONE' RECORDS:")
    
    # Check if all timing columns are 0
    all_zero_mask = (
        (none_records['Same Week'] == 0) & 
        (none_records['1 wk before'] == 0) & 
        (none_records['2 wk before'] == 0) & 
        (none_records['1 wk after'] == 0) & 
        (none_records['2 wk after'] == 0)
    )
    print(f"Records with all timing columns = 0: {all_zero_mask.sum()}")
    
    # Check for NaN values
    any_nan_mask = none_records[timing_cols].isna().any(axis=1)
    print(f"Records with any NaN in timing columns: {any_nan_mask.sum()}")
    
    # Check for other values
    other_values_mask = ~all_zero_mask & ~any_nan_mask
    print(f"Records with other timing values: {other_values_mask.sum()}")
    
    if other_values_mask.sum() > 0:
        print(f"\nSample records with other values:")
        other_sample = none_records[other_values_mask].head(5)
        for idx, (_, row) in enumerate(other_sample.iterrows()):
            print(f"  Record {idx + 1}: {[row[col] for col in timing_cols]}")
    
    # Suggest fixes
    print(f"\n🔧 POTENTIAL ISSUES & FIXES:")
    
    if all_zero_mask.sum() == len(none_records):
        print(f"✅ All 'None' records have timing values of 0 - this is expected behavior")
        print(f"   These represent promotions with no competitor timing relationship")
    elif any_nan_mask.sum() > 0:
        print(f"⚠️  {any_nan_mask.sum()} records have NaN values in timing columns")
        print(f"   Consider: Fill NaN with 0 or handle missing data differently")
    elif other_values_mask.sum() > 0:
        print(f"⚠️  {other_values_mask.sum()} records have unexpected timing values")
        print(f"   Consider: Check for values other than 0/1 in timing columns")
    
    # Check if the issue is with boolean handling
    print(f"\n🔍 BOOLEAN HANDLING CHECK:")
    same_week_types = df['Same Week'].apply(type).value_counts()
    print(f"Same Week column types: {same_week_types}")
    
    return df, none_records

if __name__ == "__main__":
    df, none_records = investigate_time_bands()
    print(f"\n✅ Investigation complete!")
    print(f"   - Total records: {len(df)}")
    print(f"   - 'None' records: {len(none_records)} ({len(none_records)/len(df)*100:.1f}%)")
