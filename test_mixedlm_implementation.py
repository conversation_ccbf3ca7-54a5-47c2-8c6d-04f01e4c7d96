#!/usr/bin/env python3
"""
Test script for MixedLM implementation
This script tests the hierarchical model implementation before running in the notebook.
"""

import pandas as pd
import numpy as np
import statsmodels.formula.api as smf
import statsmodels.api as sm
from statsmodels.regression.mixed_linear_model import MixedLM

def test_mixedlm_basic():
    """Test basic MixedLM functionality with synthetic data"""
    print("🧪 Testing MixedLM with synthetic data...")
    
    # Create synthetic hierarchical data
    np.random.seed(42)
    n_groups = 5
    n_per_group = 20
    
    data = []
    for group in range(n_groups):
        group_effect = np.random.normal(0, 1)  # Random intercept for each group
        
        for i in range(n_per_group):
            x1 = np.random.normal(0, 1)
            x2 = np.random.normal(0, 1)
            
            # Generate y with group-specific intercept
            y = 2 + group_effect + 0.5 * x1 + 0.3 * x2 + np.random.normal(0, 0.5)
            
            data.append({
                'group': f'Group_{group}',
                'x1': x1,
                'x2': x2,
                'y': y
            })
    
    df_test = pd.DataFrame(data)
    print(f"   • Created test data: {df_test.shape}")
    print(f"   • Groups: {df_test['group'].nunique()}")
    
    # Test MixedLM
    try:
        formula = "y ~ x1 + x2"
        md = smf.mixedlm(formula, df_test, groups='group')
        model_fit = md.fit()
        
        print(f"   ✓ MixedLM fitted successfully!")
        print(f"   • AIC: {model_fit.aic:.2f}")
        print(f"   • Converged: {model_fit.converged}")
        print(f"   • Observations: {model_fit.nobs}")
        
        # Check coefficients
        print(f"   • Coefficients:")
        for param, coef in model_fit.params.items():
            print(f"     - {param}: {coef:.4f}")
            
        return True
        
    except Exception as e:
        print(f"   ❌ MixedLM test failed: {e}")
        return False

def test_with_actual_data():
    """Test with the actual promotional data if available"""
    print("\n🧪 Testing with actual promotional data...")
    
    try:
        # Try to load the actual data
        DATA_PATH = "greater than 10 new_test_output_ads_v3.csv"
        df_raw = pd.read_csv(DATA_PATH, index_col=0)
        print(f"   ✓ Data loaded: {df_raw.shape}")
        
        # Basic data preparation (simplified version)
        required_cols = ['Retailer', 'ABI Mechanic', 'ABI vs Segment PTC Index Agg', 'Avg Temp']
        
        if all(col in df_raw.columns for col in required_cols):
            df_test = df_raw[required_cols].copy()
            df_test = df_test.dropna()
            
            # Clean names
            df_test['Retailer_clean'] = df_test['Retailer'].str.replace(' ', '_')
            df_test['ABI_Mechanic_clean'] = df_test['ABI Mechanic'].str.replace(' ', '_').str.replace('+', 'plus')
            df_test['y'] = df_test['ABI vs Segment PTC Index Agg']
            df_test['temp_scaled'] = (df_test['Avg Temp'] - df_test['Avg Temp'].mean()) / df_test['Avg Temp'].std()
            
            print(f"   • Cleaned data: {df_test.shape}")
            print(f"   • Retailers: {df_test['Retailer_clean'].nunique()}")
            
            # Test simple MixedLM
            formula = "y ~ ABI_Mechanic_clean + temp_scaled"
            md = smf.mixedlm(formula, df_test, groups='Retailer_clean')
            model_fit = md.fit(method='lbfgs', maxiter=1000)
            
            print(f"   ✓ Real data MixedLM fitted!")
            print(f"   • AIC: {model_fit.aic:.2f}")
            print(f"   • Converged: {model_fit.converged}")
            
            return True
        else:
            print(f"   ⚠ Missing required columns")
            return False
            
    except FileNotFoundError:
        print(f"   ⚠ Data file not found - skipping real data test")
        return True
    except Exception as e:
        print(f"   ❌ Real data test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🔧 TESTING MIXEDLM IMPLEMENTATION")
    print("=" * 50)
    
    # Test 1: Basic functionality
    test1_passed = test_mixedlm_basic()
    
    # Test 2: Real data (if available)
    test2_passed = test_with_actual_data()
    
    print(f"\n📊 TEST RESULTS:")
    print(f"   • Basic MixedLM test: {'✓ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   • Real data test: {'✓ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 All tests passed! MixedLM implementation is ready.")
        return True
    else:
        print(f"\n⚠ Some tests failed. Check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
