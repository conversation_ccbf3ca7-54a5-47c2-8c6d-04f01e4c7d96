# Hierarchical Mixed-Effects Model for Promotional Uplift Analysis

## Project Overview

This project implements a comprehensive hierarchical (mixed-effects) model to analyze promotional uplift patterns across retail channels. The analysis examines how promotional timing, brand positioning, pack sizes, and environmental factors influence ABI market share uplift, while accounting for retailer-level heterogeneity.

## Business Context

Understanding promotional effectiveness is crucial for optimizing marketing spend and maximizing market share gains. This analysis provides data-driven insights into:

- **Promotional Timing**: How the timing of promotions relative to competitor activities affects uplift
- **Brand Dynamics**: Differences in promotional response between ABI and competitor brands  
- **Pack Size Effects**: How different package formats respond to promotional strategies
- **Retailer Variation**: Accounting for systematic differences across retail channels
- **Environmental Factors**: The role of temperature/seasonality in promotional effectiveness

## Key Features

### 🔧 **Advanced Statistical Modeling**
- Mixed-effects regression with random intercepts and slopes
- Retailer-level clustering to account for unobserved heterogeneity
- Comprehensive interaction effects between key business variables
- Robust model validation and diagnostics

### 📊 **Comprehensive Data Processing**
- Automated categorical recoding for promotional timing windows
- Regex-based pack size extraction from SKU strings
- Brand segmentation using keyword matching
- Missing value imputation with group-specific strategies

### 🎯 **Business-Ready Insights**
- Executive summary with actionable recommendations
- Margin plots showing predicted uplift by key segments
- Statistical significance testing for all effects
- Cross-validation for predictive performance assessment

## Repository Structure

```
├── hierarchical_model.ipynb    # Main analysis notebook
├── requirements.txt           # Python dependencies
├── README.md                 # This file
├── .gitignore               # Git ignore rules
└── greater than 10 new_test_output_ads_v3.csv  # Input data
```

## Getting Started

### Prerequisites

- Python 3.10+ 
- Jupyter Notebook or JupyterLab
- Required packages (see requirements.txt)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd hierarchical-model-analysis
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Launch Jupyter**
   ```bash
   jupyter notebook hierarchical_model.ipynb
   ```

4. **Run the analysis**
   - Execute all cells sequentially from top to bottom
   - The notebook is designed for complete reproducibility

## Data Requirements

The analysis expects a CSV file with the following key columns:

- **Retailer**: Retail channel identifier
- **ABI SKU**: Product SKU for ABI brands
- **Competitor SKU**: Product SKU for competitor brands  
- **Timing columns**: Overlapping, Same Week, 1 wk before/after, 2 wk before/after
- **Avg Temp**: Average temperature during promotional period
- **ABI MS Promo Uplift - abs**: Target variable (absolute market share uplift)

## Model Specification

### Fixed Effects
- **Time Band**: Before/Same/After promotional timing
- **Pack Band**: Package size categories (extracted from SKUs)
- **Brand Segment**: ABI vs Competition classification
- **Temperature**: Standardized average temperature
- **Interactions**: Key business-relevant interaction terms

### Random Effects
- **Random Intercepts**: Retailer-specific baseline uplift levels
- **Random Slopes**: Retailer-specific sensitivity to promotional timing

### Outcome Variable
- ABI Market Share Promotional Uplift (absolute)
- Log-transformation applied if highly skewed

## Key Results

The analysis provides insights into:

1. **Promotional Timing Effectiveness**: Which timing strategies drive the highest uplift
2. **Brand-Specific Patterns**: How ABI vs competitor brands respond differently
3. **Pack Size Optimization**: Optimal promotional strategies by package format
4. **Retailer Segmentation**: Identification of high/low performing retail channels
5. **Seasonal Considerations**: Temperature/weather effects on promotional response

## Model Validation

- **Residual Analysis**: Q-Q plots and fitted vs. observed diagnostics
- **Cross-Validation**: 5-fold CV with RMSE and R² metrics
- **Likelihood Ratio Tests**: Comparison against simpler model specifications
- **Random Effects Analysis**: Examination of retailer-level variation patterns

## Business Applications

### Strategic Planning
- Optimize promotional calendar timing
- Develop retailer-specific promotional strategies
- Align pack size and brand positioning with timing

### Performance Monitoring  
- Track promotional effectiveness over time
- Identify underperforming retail channels
- Measure ROI of promotional investments

### Tactical Execution
- Real-time promotional optimization
- Competitive response strategies
- Seasonal promotional planning

## Technical Notes

- **Software**: Python with statsmodels, pandas, numpy, matplotlib, seaborn
- **Model Engine**: statsmodels.formula.api.mixedlm
- **Validation**: scikit-learn cross-validation framework
- **Visualization**: matplotlib and seaborn for business-ready charts

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/new-analysis`)
3. Commit changes (`git commit -am 'Add new analysis'`)
4. Push to branch (`git push origin feature/new-analysis`)
5. Create a Pull Request

## License

This project is proprietary to Anheuser-Busch InBev. All rights reserved.

## Contact

For questions about this analysis, please contact the Analytics Team.

---

*Last updated: 2025-07-02*
