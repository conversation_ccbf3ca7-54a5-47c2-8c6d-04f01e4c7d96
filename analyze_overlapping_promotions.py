#!/usr/bin/env python3
"""
Analyze the impact of combining 'Same Week' and 'Overlapping' promotions
into a single time band category.
"""

import pandas as pd
import numpy as np

def analyze_overlapping_approach():
    print("🔍 Analyzing Same Week + Overlapping Combination...")
    
    # Load and process data
    df = pd.read_csv("greater than 10 new_test_output_ads_v3.csv", index_col=0)
    
    # Check if we have overlapping column
    print("📋 Available columns:")
    timing_related_cols = [col for col in df.columns if any(word in col.lower() 
                          for word in ['same', 'week', 'before', 'after', 'overlap', 'start', 'end'])]
    for col in timing_related_cols:
        print(f"  - {col}")
    
    # Define timing columns
    timing_cols = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']
    
    # Convert to numeric
    for col in timing_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    print(f"\n📊 CURRENT TIME BAND ANALYSIS:")
    print(f"Total records: {len(df)}")
    
    # Current approach
    def classify_current_time_band(row):
        same_week = 1 if row['Same Week'] == 1 else 0
        
        if same_week == 1:
            return "Same"
        elif row['1 wk before'] == 1 or row['2 wk before'] == 1:
            return "Before"
        elif row['1 wk after'] == 1 or row['2 wk after'] == 1:
            return "After"
        else:
            return "None"
    
    df['current_time_band'] = df.apply(classify_current_time_band, axis=1)
    current_counts = df['current_time_band'].value_counts()
    
    print("Current time band distribution:")
    for band, count in current_counts.items():
        print(f"  {band}: {count} ({count/len(df)*100:.1f}%)")
    
    # Check for potential overlapping scenarios
    print(f"\n🔍 ANALYZING OVERLAPPING SCENARIOS:")
    
    # Check records with multiple timing indicators
    df['multiple_timing'] = (
        (df['Same Week'] == 1).astype(int) +
        (df['1 wk before'] == 1).astype(int) +
        (df['2 wk before'] == 1).astype(int) +
        (df['1 wk after'] == 1).astype(int) +
        (df['2 wk after'] == 1).astype(int)
    )
    
    multi_timing_counts = df['multiple_timing'].value_counts().sort_index()
    print("Records with multiple timing indicators:")
    for count, records in multi_timing_counts.items():
        print(f"  {count} timing indicators: {records} records")
    
    # Analyze records with multiple timing indicators
    multi_timing_records = df[df['multiple_timing'] > 1]
    if len(multi_timing_records) > 0:
        print(f"\n📋 SAMPLE RECORDS WITH MULTIPLE TIMING INDICATORS:")
        sample_multi = multi_timing_records[timing_cols + ['current_time_band']].head(10)
        for idx, (_, row) in enumerate(sample_multi.iterrows()):
            print(f"  {idx+1}. SW={row['Same Week']}, 1B={row['1 wk before']}, 2B={row['2 wk before']}, 1A={row['1 wk after']}, 2A={row['2 wk after']} -> {row['current_time_band']}")
    
    # Proposed new approach: Combine overlapping scenarios
    print(f"\n🆕 PROPOSED NEW TIME BAND APPROACH:")
    
    def classify_new_time_band(row):
        same_week = 1 if row['Same Week'] == 1 else 0
        before_1 = 1 if row['1 wk before'] == 1 else 0
        before_2 = 1 if row['2 wk before'] == 1 else 0
        after_1 = 1 if row['1 wk after'] == 1 else 0
        after_2 = 1 if row['2 wk after'] == 1 else 0
        
        # Count active timing windows
        total_active = same_week + before_1 + before_2 + after_1 + after_2
        
        if total_active == 0:
            return "None"
        elif same_week == 1:
            # If same week is active, classify as "Concurrent" regardless of other timing
            return "Concurrent"
        elif before_1 == 1 or before_2 == 1:
            return "Before"
        elif after_1 == 1 or after_2 == 1:
            return "After"
        else:
            return "None"
    
    df['new_time_band'] = df.apply(classify_new_time_band, axis=1)
    new_counts = df['new_time_band'].value_counts()
    
    print("New time band distribution:")
    for band, count in new_counts.items():
        print(f"  {band}: {count} ({count/len(df)*100:.1f}%)")
    
    # Alternative approach: More granular overlapping categories
    print(f"\n🔄 ALTERNATIVE APPROACH - GRANULAR OVERLAPPING:")
    
    def classify_granular_time_band(row):
        same_week = 1 if row['Same Week'] == 1 else 0
        before_1 = 1 if row['1 wk before'] == 1 else 0
        before_2 = 1 if row['2 wk before'] == 1 else 0
        after_1 = 1 if row['1 wk after'] == 1 else 0
        after_2 = 1 if row['2 wk after'] == 1 else 0
        
        # Count active timing windows
        total_active = same_week + before_1 + before_2 + after_1 + after_2
        
        if total_active == 0:
            return "None"
        elif total_active > 1:
            # Multiple timing windows active - this is overlapping
            if same_week == 1:
                return "Concurrent_Plus"  # Same week + other timing
            else:
                return "Multi_Window"     # Multiple before/after windows
        elif same_week == 1:
            return "Concurrent_Only"
        elif before_1 == 1 or before_2 == 1:
            return "Before_Only"
        elif after_1 == 1 or after_2 == 1:
            return "After_Only"
        else:
            return "None"
    
    df['granular_time_band'] = df.apply(classify_granular_time_band, axis=1)
    granular_counts = df['granular_time_band'].value_counts()
    
    print("Granular time band distribution:")
    for band, count in granular_counts.items():
        print(f"  {band}: {count} ({count/len(df)*100:.1f}%)")
    
    # Compare approaches
    print(f"\n📈 COMPARISON OF APPROACHES:")
    
    comparison_df = pd.DataFrame({
        'Current': current_counts,
        'New_Simple': new_counts,
        'Granular': granular_counts
    }).fillna(0).astype(int)
    
    print(comparison_df)
    
    # Business recommendations
    print(f"\n💡 BUSINESS RECOMMENDATIONS:")
    
    concurrent_records = len(df[df['new_time_band'] == 'Concurrent'])
    none_records = len(df[df['new_time_band'] == 'None'])
    
    print(f"1. SIMPLE APPROACH (Recommended):")
    print(f"   - Concurrent: {concurrent_records} ({concurrent_records/len(df)*100:.1f}%) - Direct competitive pressure")
    print(f"   - Before: {new_counts.get('Before', 0)} ({new_counts.get('Before', 0)/len(df)*100:.1f}%) - Following competitor")
    print(f"   - After: {new_counts.get('After', 0)} ({new_counts.get('After', 0)/len(df)*100:.1f}%) - Leading competitor")
    print(f"   - None: {none_records} ({none_records/len(df)*100:.1f}%) - No competitive timing pressure")
    
    print(f"\n2. BENEFITS OF NEW APPROACH:")
    print(f"   ✅ More intuitive business interpretation")
    print(f"   ✅ 'Concurrent' clearly indicates direct competitive pressure")
    print(f"   ✅ Reduces complexity while maintaining analytical value")
    print(f"   ✅ Better aligns with promotional strategy decisions")
    
    # Check what changes between current and new approach
    changes = df[df['current_time_band'] != df['new_time_band']]
    if len(changes) > 0:
        print(f"\n🔄 RECORDS THAT WOULD CHANGE CLASSIFICATION:")
        print(f"Total records changing: {len(changes)}")
        change_summary = changes.groupby(['current_time_band', 'new_time_band']).size()
        for (old, new), count in change_summary.items():
            print(f"  {old} -> {new}: {count} records")
    
    return df

if __name__ == "__main__":
    df = analyze_overlapping_approach()
    print(f"\n✅ Analysis complete!")
