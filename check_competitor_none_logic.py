#!/usr/bin/env python3
"""
Check the relationship between 'No Competitor' and 'None' time band.
These should logically be the same - if there's no competitor, 
there can't be any competitive timing relationship.
"""

import pandas as pd
import numpy as np

def check_competitor_none_relationship():
    print("🔍 Checking Competitor vs Time Band Logic...")
    
    # Load and process data
    df = pd.read_csv("greater than 10 new_test_output_ads_v3.csv", index_col=0)
    
    # Convert timing columns to numeric
    timing_cols = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']
    for col in timing_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Create competitor indicator
    df['Has_Competitor'] = df['Competitor SKU'].notna().astype(int)
    
    # Apply current improved time band logic
    def classify_improved_time_band(row):
        same_week = 1 if pd.to_numeric(row['Same Week'], errors='coerce') == 1 else 0
        overlapping = 1 if pd.to_numeric(row['Overlapping'], errors='coerce') == 1 else 0
        before_1 = 1 if row['1 wk before'] == 1 else 0
        before_2 = 1 if row['2 wk before'] == 1 else 0
        after_1 = 1 if row['1 wk after'] == 1 else 0
        after_2 = 1 if row['2 wk after'] == 1 else 0
        
        if same_week == 1 or overlapping == 1:
            return "Concurrent"
        elif before_1 == 1 or before_2 == 1:
            return "Before"
        elif after_1 == 1 or after_2 == 1:
            return "After"
        else:
            return "None"
    
    df['time_band'] = df.apply(classify_improved_time_band, axis=1)
    
    print(f"📊 OVERALL STATISTICS:")
    print(f"Total records: {len(df)}")
    
    # Competitor presence
    comp_counts = df['Has_Competitor'].value_counts()
    print(f"\nCompetitor Presence:")
    print(f"  No Competitor (0): {comp_counts.get(0, 0)} ({comp_counts.get(0, 0)/len(df)*100:.1f}%)")
    print(f"  Has Competitor (1): {comp_counts.get(1, 0)} ({comp_counts.get(1, 0)/len(df)*100:.1f}%)")
    
    # Time band distribution
    time_counts = df['time_band'].value_counts()
    print(f"\nTime Band Distribution:")
    for band, count in time_counts.items():
        print(f"  {band}: {count} ({count/len(df)*100:.1f}%)")
    
    # Cross-tabulation
    print(f"\n🔍 CROSS-TABULATION: Competitor vs Time Band")
    crosstab = pd.crosstab(df['Has_Competitor'], df['time_band'], margins=True)
    print(crosstab)
    
    # Check the logical inconsistency
    print(f"\n❓ LOGICAL ANALYSIS:")
    
    # Records with no competitor but non-None time band
    no_comp_but_timing = df[(df['Has_Competitor'] == 0) & (df['time_band'] != 'None')]
    print(f"\n🚨 INCONSISTENCY: Records with NO competitor but timing relationship:")
    print(f"Count: {len(no_comp_but_timing)}")
    
    if len(no_comp_but_timing) > 0:
        print(f"\nBreakdown by time band:")
        inconsistent_breakdown = no_comp_but_timing['time_band'].value_counts()
        for band, count in inconsistent_breakdown.items():
            print(f"  {band}: {count} records")
        
        print(f"\nSample inconsistent records:")
        sample_cols = ['Competitor SKU', 'Same Week', 'Overlapping', '1 wk before', 
                      '2 wk before', '1 wk after', '2 wk after', 'time_band']
        sample = no_comp_but_timing[sample_cols].head(10)
        for idx, (_, row) in enumerate(sample.iterrows()):
            comp_sku = "None" if pd.isna(row['Competitor SKU']) else str(row['Competitor SKU'])[:20]
            print(f"  {idx+1}. Competitor: {comp_sku} | SW={row['Same Week']}, OL={row['Overlapping']}, "
                  f"1B={row['1 wk before']}, 1A={row['1 wk after']} -> {row['time_band']}")
    
    # Records with competitor but None time band
    has_comp_but_none = df[(df['Has_Competitor'] == 1) & (df['time_band'] == 'None')]
    print(f"\n✅ EXPECTED: Records with competitor but 'None' time band:")
    print(f"Count: {len(has_comp_but_none)} (these are fine - competitor exists but no timing overlap)")
    
    # The ideal scenario
    no_comp_and_none = df[(df['Has_Competitor'] == 0) & (df['time_band'] == 'None')]
    print(f"\n✅ CORRECT: Records with NO competitor AND 'None' time band:")
    print(f"Count: {len(no_comp_and_none)}")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if len(no_comp_but_timing) > 0:
        print(f"🚨 ISSUE FOUND: {len(no_comp_but_timing)} records have timing relationships without competitors!")
        print(f"   This suggests:")
        print(f"   1. Data quality issue - timing columns populated incorrectly")
        print(f"   2. Missing competitor data")
        print(f"   3. Logic error in timing calculation")
        
        print(f"\n🔧 SUGGESTED FIXES:")
        print(f"   Option 1: Force 'None' time band when Has_Competitor = 0")
        print(f"   Option 2: Investigate data source for timing columns")
        print(f"   Option 3: Check if timing refers to something other than competitor timing")
    else:
        print(f"✅ NO ISSUES: Logic is consistent!")
    
    # Calculate what the corrected distribution would look like
    print(f"\n📊 CORRECTED DISTRIBUTION (if we fix the logic):")
    
    # Force None for records with no competitor
    df_corrected = df.copy()
    df_corrected.loc[df_corrected['Has_Competitor'] == 0, 'time_band'] = 'None'
    
    corrected_counts = df_corrected['time_band'].value_counts()
    print(f"Corrected time band distribution:")
    for band, count in corrected_counts.items():
        print(f"  {band}: {count} ({count/len(df)*100:.1f}%)")
    
    return df, no_comp_but_timing

if __name__ == "__main__":
    df, inconsistent = check_competitor_none_relationship()
    print(f"\n✅ Analysis complete!")
