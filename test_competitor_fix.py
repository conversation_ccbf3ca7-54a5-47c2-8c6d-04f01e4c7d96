#!/usr/bin/env python3
"""
Quick test to verify the competitor presence fix works correctly.
"""

import pandas as pd
import matplotlib.pyplot as plt

def test_competitor_labels():
    print("🧪 Testing competitor presence label fix...")
    
    # Simulate the data
    # 596 records with competitor (1), 93 without (0)
    has_competitor = [1] * 596 + [0] * 93
    df_test = pd.DataFrame({'Has_Competitor': has_competitor})
    
    print(f"Test data: {len(df_test)} records")
    print(f"Has Competitor (1): {sum(df_test['Has_Competitor'])}")
    print(f"No Competitor (0): {len(df_test) - sum(df_test['Has_Competitor'])}")
    
    # Test value_counts() behavior
    competitor_counts = df_test['Has_Competitor'].value_counts()
    print(f"\nvalue_counts() result:")
    print(competitor_counts)
    print(f"Index order: {competitor_counts.index.tolist()}")
    print(f"Values: {competitor_counts.values.tolist()}")
    
    # Test old (wrong) labels
    old_labels = ['No Competitor', 'Has Competitor']
    print(f"\n❌ OLD (WRONG) LABELS: {old_labels}")
    print(f"Would show: {old_labels[0]} = {competitor_counts.values[0]} ({competitor_counts.values[0]/len(df_test)*100:.1f}%)")
    print(f"Would show: {old_labels[1]} = {competitor_counts.values[1]} ({competitor_counts.values[1]/len(df_test)*100:.1f}%)")
    
    # Test new (correct) labels
    new_labels = ['Has Competitor', 'No Competitor']
    print(f"\n✅ NEW (CORRECT) LABELS: {new_labels}")
    print(f"Will show: {new_labels[0]} = {competitor_counts.values[0]} ({competitor_counts.values[0]/len(df_test)*100:.1f}%)")
    print(f"Will show: {new_labels[1]} = {competitor_counts.values[1]} ({competitor_counts.values[1]/len(df_test)*100:.1f}%)")
    
    # Create a small test plot
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # Wrong version
    ax1.pie(competitor_counts.values, labels=old_labels, autopct='%1.1f%%', startangle=90)
    ax1.set_title('❌ WRONG: Old Labels')
    
    # Correct version  
    ax2.pie(competitor_counts.values, labels=new_labels, autopct='%1.1f%%', startangle=90)
    ax2.set_title('✅ CORRECT: New Labels')
    
    plt.tight_layout()
    plt.savefig('competitor_fix_comparison.png', dpi=120, bbox_inches='tight')
    print(f"\n📊 Comparison chart saved as 'competitor_fix_comparison.png'")
    
    return True

if __name__ == "__main__":
    test_competitor_labels()
    print(f"\n✅ Test completed! The fix should now show:")
    print(f"   - Has Competitor: 86.5%")
    print(f"   - No Competitor: 13.5%")
