#!/usr/bin/env python3
"""
Analyze the time band logic to understand why there are many 'None' records.
"""

import pandas as pd
import numpy as np

def analyze_time_bands():
    print("🔍 Analyzing Time Band Logic...")
    
    # Load and process data
    df = pd.read_csv("greater than 10 new_test_output_ads_v3.csv", index_col=0)
    
    # Select timing columns
    timing_cols = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']
    
    # Convert to numeric
    for col in timing_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    print(f"📊 TIMING COLUMN ANALYSIS:")
    print(f"Total records: {len(df)}")
    
    # Analyze each timing column
    for col in timing_cols:
        print(f"\n{col}:")
        counts = df[col].value_counts(dropna=False).sort_index()
        print(f"  Value counts: {dict(counts)}")
        print(f"  Records with value 1: {counts.get(1, 0)}")
        print(f"  Records with value 0: {counts.get(0, 0)}")
    
    # Analyze combinations
    print(f"\n🔍 TIMING COMBINATION ANALYSIS:")
    
    # Check records with all zeros
    all_zero_mask = (
        (df['Same Week'] == 0) & 
        (df['1 wk before'] == 0) & 
        (df['2 wk before'] == 0) & 
        (df['1 wk after'] == 0) & 
        (df['2 wk after'] == 0)
    )
    all_zero_count = all_zero_mask.sum()
    print(f"Records with ALL timing = 0: {all_zero_count} ({all_zero_count/len(df)*100:.1f}%)")
    
    # Check records with Same Week = TRUE
    same_week_true = (df['Same Week'] == 1).sum()
    print(f"Records with Same Week = 1: {same_week_true}")
    
    # Check records with any before timing
    any_before = ((df['1 wk before'] == 1) | (df['2 wk before'] == 1)).sum()
    print(f"Records with any 'before' timing: {any_before}")
    
    # Check records with any after timing  
    any_after = ((df['1 wk after'] == 1) | (df['2 wk after'] == 1)).sum()
    print(f"Records with any 'after' timing: {any_after}")
    
    # Apply the time band logic
    def classify_time_band(row):
        same_week = 1 if row['Same Week'] == 1 else 0
        
        if same_week == 1:
            return "Same"
        elif row['1 wk before'] == 1 or row['2 wk before'] == 1:
            return "Before"
        elif row['1 wk after'] == 1 or row['2 wk after'] == 1:
            return "After"
        else:
            return "None"
    
    df['time_band'] = df.apply(classify_time_band, axis=1)
    
    print(f"\n📈 TIME BAND RESULTS:")
    time_counts = df['time_band'].value_counts()
    for band, count in time_counts.items():
        print(f"  {band}: {count} ({count/len(df)*100:.1f}%)")
    
    # Analyze what "None" means
    print(f"\n🤔 WHAT DOES 'NONE' MEAN?")
    none_records = df[df['time_band'] == 'None']
    
    # Check if None records have competitors
    none_with_competitor = none_records['Competitor SKU'].notna().sum()
    none_without_competitor = none_records['Competitor SKU'].isna().sum()
    
    print(f"'None' records with competitor data: {none_with_competitor}")
    print(f"'None' records without competitor data: {none_without_competitor}")
    
    # Sample some None records
    print(f"\n📋 SAMPLE 'NONE' RECORDS:")
    sample_none = none_records[['Retailer', 'ABI Mechanic', 'Competitor SKU', 'Same Week', 
                               '1 wk before', '2 wk before', '1 wk after', '2 wk after']].head(10)
    
    for idx, (_, row) in enumerate(sample_none.iterrows()):
        has_comp = "Yes" if pd.notna(row['Competitor SKU']) else "No"
        print(f"  {idx+1}. {row['Retailer']} | {row['ABI Mechanic']} | Competitor: {has_comp}")
        print(f"     Timing: SW={row['Same Week']}, 1B={row['1 wk before']}, 2B={row['2 wk before']}, 1A={row['1 wk after']}, 2A={row['2 wk after']}")
    
    print(f"\n💡 INTERPRETATION:")
    print(f"'None' time band means:")
    print(f"  - The ABI promotion has NO specific timing relationship with competitor promotions")
    print(f"  - It could be:")
    print(f"    • A promotion with no competitor activity in the analyzed time windows")
    print(f"    • A promotion where competitor activity exists but outside the ±2 week window")
    print(f"    • A standalone promotion in a market with limited competition")
    print(f"  - This is NORMAL and EXPECTED in promotional analysis")
    
    # Business recommendations
    print(f"\n🎯 BUSINESS IMPLICATIONS:")
    print(f"  - {all_zero_count} promotions ({all_zero_count/len(df)*100:.1f}%) have no competitive timing pressure")
    print(f"  - These could be opportunities for:")
    print(f"    • Testing promotional effectiveness in 'clean' environments")
    print(f"    • Understanding baseline promotional performance")
    print(f"    • Identifying markets with low competitive intensity")
    
    return df

if __name__ == "__main__":
    df = analyze_time_bands()
    print(f"\n✅ Analysis complete!")
