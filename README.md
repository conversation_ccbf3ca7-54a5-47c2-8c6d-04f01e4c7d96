# Hierarchical Mixed-Effects Model for Promotional Analysis

## 📋 Project Overview

This project implements a **hierarchical (mixed-effects) model** using `statsmodels.MixedLM` to analyze promotional effectiveness patterns across different retailers. The model helps identify key drivers of promotional success while accounting for retailer-specific variations.

**🆕 Updated Implementation**: Now uses statsmodels.MixedLM with robust model selection, comprehensive error handling, and automated testing.

## 🎯 Business Objectives

- **Identify Key Drivers**: Determine which promotional mechanics, timing, and environmental factors drive effectiveness
- **Account for Retailer Variation**: Use hierarchical modeling to capture retailer-specific effects
- **Optimize Promotional Strategy**: Provide actionable insights for promotional planning and execution
- **Quantify Effectiveness**: Measure promotional impact using ABI vs Segment PTC Index

## 🔧 Technical Implementation

### Model Architecture
- **Model Type**: Mixed-Effects Linear Model (statsmodels.MixedLM)
- **Fixed Effects**: Promotional mechanics, timing bands, temperature, coverage, duration
- **Random Effects**: Retailer-level intercepts (accounts for retailer-specific baseline performance)
- **Outcome Variable**: ABI vs Segment PTC Index (promotional effectiveness measure)

### Key Features
- **Robust Model Selection**: Tests multiple model specifications and selects best-fitting model
- **Error Handling**: Graceful fallback to simpler models if convergence issues occur
- **Comprehensive Diagnostics**: Model fit assessment, residual analysis, and business interpretation
- **Automated Testing**: Test script validates implementation before notebook execution

## 📁 Project Structure

```
Hierarchical_from_Lib/
├── hierarchical_model_simplified.ipynb    # Main analysis notebook
├── test_mixedlm_implementation.py         # Test script for MixedLM
├── greater than 10 new_test_output_ads_v3.csv  # Input data
└── README.md                              # This file
```

## 🚀 Getting Started

### Prerequisites
```bash
# Required packages
pandas>=1.5.0
numpy>=1.20.0
statsmodels>=0.13.0
matplotlib>=3.5.0
seaborn>=0.11.0
scikit-learn>=1.0.0
```

### Installation
```bash
# Activate conda environment
conda activate base

# Install required packages (if not already installed)
conda install pandas numpy statsmodels matplotlib seaborn scikit-learn
```

### Running the Analysis

1. **Test the Implementation** (recommended first step):
   ```bash
   python test_mixedlm_implementation.py
   ```

2. **Run the Main Analysis**:
   - Open `hierarchical_model_simplified.ipynb` in Jupyter
   - Execute cells sequentially
   - The notebook will automatically handle model selection and diagnostics

## Data Requirements

The analysis expects a CSV file with the following key columns:

- **Retailer**: Retail channel identifier
- **ABI SKU**: Product SKU for ABI brands
- **Competitor SKU**: Product SKU for competitor brands  
- **Timing columns**: Overlapping, Same Week, 1 wk before/after, 2 wk before/after
- **Avg Temp**: Average temperature during promotional period
- **ABI MS Promo Uplift - abs**: Target variable (absolute market share uplift)

## Model Specification

### Fixed Effects
- **Time Band**: Before/Same/After promotional timing
- **Pack Band**: Package size categories (extracted from SKUs)
- **Brand Segment**: ABI vs Competition classification
- **Temperature**: Standardized average temperature
- **Interactions**: Key business-relevant interaction terms

### Random Effects
- **Random Intercepts**: Retailer-specific baseline uplift levels
- **Random Slopes**: Retailer-specific sensitivity to promotional timing

### Outcome Variable
- ABI Market Share Promotional Uplift (absolute)
- Log-transformation applied if highly skewed

## Key Results

The analysis provides insights into:

1. **Promotional Timing Effectiveness**: Which timing strategies drive the highest uplift
2. **Brand-Specific Patterns**: How ABI vs competitor brands respond differently
3. **Pack Size Optimization**: Optimal promotional strategies by package format
4. **Retailer Segmentation**: Identification of high/low performing retail channels
5. **Seasonal Considerations**: Temperature/weather effects on promotional response

## Model Validation

- **Residual Analysis**: Q-Q plots and fitted vs. observed diagnostics
- **Cross-Validation**: 5-fold CV with RMSE and R² metrics
- **Likelihood Ratio Tests**: Comparison against simpler model specifications
- **Random Effects Analysis**: Examination of retailer-level variation patterns

## Business Applications

### Strategic Planning
- Optimize promotional calendar timing
- Develop retailer-specific promotional strategies
- Align pack size and brand positioning with timing

### Performance Monitoring  
- Track promotional effectiveness over time
- Identify underperforming retail channels
- Measure ROI of promotional investments

### Tactical Execution
- Real-time promotional optimization
- Competitive response strategies
- Seasonal promotional planning

## Technical Notes

- **Software**: Python with statsmodels, pandas, numpy, matplotlib, seaborn
- **Model Engine**: statsmodels.formula.api.mixedlm
- **Validation**: scikit-learn cross-validation framework
- **Visualization**: matplotlib and seaborn for business-ready charts

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/new-analysis`)
3. Commit changes (`git commit -am 'Add new analysis'`)
4. Push to branch (`git push origin feature/new-analysis`)
5. Create a Pull Request

## License

This project is proprietary to Anheuser-Busch InBev. All rights reserved.

## Contact

For questions about this analysis, please contact the Analytics Team.

---

*Last updated: 2025-07-02*
