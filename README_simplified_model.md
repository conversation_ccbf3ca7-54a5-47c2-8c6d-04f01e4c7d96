# Simplified Hierarchical Mixed-Effects Model for Promotional Analysis

## Project Overview

This project implements a **focused hierarchical (mixed-effects) model** using a carefully selected subset of promotional variables to analyze effectiveness patterns across retail channels. The simplified approach provides clear, actionable insights while maintaining statistical rigor.

## Key Features

### 🎯 **Focused Variable Selection**
- **ABI Mechanic**: Promotional mechanism types (Immediate, LV, No_NIP, etc.)
- **Promotional Timing**: Same week, before/after competitor activities
- **Environmental Factors**: Average temperature effects
- **Coverage & Duration**: Promotional scope and length
- **Competition**: Presence of competitor activities

### 📊 **Advanced Analytics**
- Mixed-effects modeling with retailer-level random effects
- Comprehensive missing value treatment strategies
- Robust model diagnostics and validation
- Business-friendly interpretation framework

### 🚀 **Business Value**
- **Outcome Variable**: ABI vs Segment PTC Index (promotional effectiveness measure)
- **Retailer Insights**: Identification of high/low performing channels
- **Strategic Guidance**: Data-driven promotional optimization recommendations
- **Operational Impact**: Quantified effects of promotional variables

## Repository Structure

```
├── hierarchical_model_simplified.ipynb    # Main analysis notebook
├── validate_notebook_workflow.py          # Comprehensive validation script
├── test_simplified_model.py              # Component testing script
├── test_column_mapping.py                # Column validation script
├── README_simplified_model.md            # This documentation
└── greater than 10 new_test_output_ads_v3.csv  # Input data
```

## Quick Start

### Prerequisites
- Python 3.10+
- Required packages: pandas, numpy, statsmodels, matplotlib, seaborn, scipy, sklearn

### Running the Analysis

1. **Validate the workflow** (recommended first step):
   ```bash
   python validate_notebook_workflow.py
   ```

2. **Open the main notebook**:
   ```bash
   jupyter notebook hierarchical_model_simplified.ipynb
   ```

3. **Run all cells sequentially** from top to bottom for complete analysis

## Data Requirements

The model uses **11 core variables** from the original dataset:

### Required Columns
- `Retailer` - Retail channel identifier (for hierarchical grouping)
- `ABI Mechanic` - Promotional mechanism type
- `Same Week`, `1 wk after`, `2 wk after`, `1 wk before`, `2 wk before` - Timing variables
- `Avg Temp` - Environmental factor
- `ABI Coverage` - Promotional coverage measure
- `ABI vs Segment PTC Index Agg` - **Outcome variable** (promotional effectiveness)
- `Competitor SKU` - Competitor presence indicator
- `ABI Start`, `ABI End` - For duration calculation

### Calculated Variables
- `ABI_Duration_Days` - Promotional duration (calculated from start/end dates)
- `time_band` - Categorical timing (Before/Same/After/None)
- `Has_Competitor` - Binary competitor presence indicator

## Model Specification

### Fixed Effects
- **ABI Mechanic** (categorical): Different promotional mechanisms
- **Time Band** (ordered categorical): Promotional timing relative to competitors
- **Temperature** (scaled continuous): Environmental effects
- **Coverage** (scaled continuous): Promotional scope
- **Duration** (scaled continuous): Length of promotional period
- **Competition** (binary): Presence of competitor activities

### Random Effects
- **Retailer-level intercepts**: Account for baseline differences across retailers
- **Retailer-level slopes** (if data supports): Differential responses to timing

### Outcome Variable
- **ABI vs Segment PTC Index**: Measures promotional effectiveness relative to segment performance

## Key Results

Based on validation testing, the model identifies **4 significant drivers**:

1. **ABI_Mechanic[LV]**: Decreases effectiveness by 0.049 units (p=0.010)
2. **ABI_Mechanic[No_NIP]**: Decreases effectiveness by 0.138 units (p<0.001)
3. **Temperature**: Increases effectiveness by 0.025 units per SD (p<0.001)
4. **Coverage**: Decreases effectiveness by 0.018 units per SD (p=0.001)

## Business Applications

### Strategic Planning
- **Promotional Mechanism Optimization**: Focus on most effective ABI mechanics
- **Timing Strategy**: Leverage competitor timing insights for maximum impact
- **Seasonal Planning**: Incorporate temperature effects into promotional calendar

### Operational Excellence
- **Retailer Segmentation**: Develop channel-specific promotional strategies
- **Resource Allocation**: Optimize promotional spend based on effectiveness patterns
- **Performance Monitoring**: Track promotional ROI across different mechanisms

### Competitive Intelligence
- **Market Positioning**: Understand competitive promotional dynamics
- **Response Strategies**: Develop data-driven competitive responses
- **Opportunity Identification**: Find gaps in competitive promotional coverage

## Technical Notes

### Model Robustness
- Handles missing values with retailer-specific imputation strategies
- Scales numeric predictors for optimal convergence
- Includes comprehensive model diagnostics and validation
- Tests multiple model specifications for robustness

### Validation Framework
- **Component Testing**: Individual function validation
- **Workflow Testing**: End-to-end process validation
- **Model Diagnostics**: Residual analysis and assumption checking
- **Business Validation**: Interpretation and insight extraction

### Reproducibility
- All code runs sequentially without manual intervention
- Clear documentation of all processing steps
- Robust error handling and fallback strategies
- Comprehensive logging of model fitting process

## Limitations and Considerations

1. **Simplified Variable Set**: Focuses on core drivers only - additional variables may provide further insights
2. **Random Effects**: Some retailers may have limited data for stable random effect estimation
3. **Temporal Dynamics**: Model captures cross-sectional patterns - time series effects not explicitly modeled
4. **Interaction Effects**: Limited to key business-relevant interactions to maintain interpretability

## Future Enhancements

### Short-term (0-3 months)
- Add cross-validation for model selection
- Implement automated model selection procedures
- Develop retailer-specific performance scorecards

### Medium-term (3-6 months)
- Expand to include additional promotional variables
- Develop predictive promotional planning tools
- Create automated reporting dashboards

### Long-term (6+ months)
- Integrate with real-time promotional data feeds
- Develop multi-outcome modeling framework
- Create competitive intelligence integration

## Support and Maintenance

For questions about this analysis:
- Review the comprehensive validation output
- Check the notebook documentation and comments
- Run the validation scripts to verify setup

---

**Last Updated**: 2025-07-02  
**Model Version**: 1.0 (Simplified)  
**Status**: Production Ready
