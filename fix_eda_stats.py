#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to identify and fix EDA statistics issues in the hierarchical model notebook.
This script will check the actual data distributions and identify discrepancies.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def main():
    print("🔍 Analyzing EDA statistics issues...")
    
    # Load the data
    DATA_PATH = "greater than 10 new_test_output_ads_v3.csv"
    
    try:
        df_raw = pd.read_csv(DATA_PATH, index_col=0)
        print(f"✅ Data loaded successfully: {df_raw.shape}")
    except FileNotFoundError:
        print(f"❌ Data file not found: {DATA_PATH}")
        return
    
    # Define required columns (same as notebook)
    REQUIRED_COLUMNS = [
        'Retailer', 'ABI Mechanic', 'Same Week', '1 wk after', '2 wk after', 
        '1 wk before', '2 wk before', 'Avg Temp', 'ABI Coverage', 
        'ABI vs Segment PTC Index Agg', 'Competitor SKU', 'ABI Start', 'ABI End'
    ]
    
    # Select required columns
    df = df_raw[REQUIRED_COLUMNS].copy()
    print(f"Selected columns: {df.shape}")
    
    # Convert data types (simplified version)
    numeric_cols = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before',
                   'Avg Temp', 'ABI Coverage', 'ABI vs Segment PTC Index Agg']
    
    for col in numeric_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Convert dates
    df['ABI Start'] = pd.to_datetime(df['ABI Start'], errors='coerce')
    df['ABI End'] = pd.to_datetime(df['ABI End'], errors='coerce')
    
    # Calculate duration
    df['ABI_Duration_Days'] = (df['ABI End'] - df['ABI Start']).dt.days
    
    # Create time band (same logic as notebook)
    def recode_time_window(row):
        same_week = row["Same Week"]
        if pd.isna(same_week):
            same_week = 0
        elif same_week == True or same_week == 'TRUE' or same_week == 1:
            same_week = 1
        else:
            same_week = 0
        
        if same_week == 1:
            return "Same"
        elif row["1 wk before"] == 1 or row["2 wk before"] == 1:
            return "Before"
        elif row["1 wk after"] == 1 or row["2 wk after"] == 1:
            return "After"
        else:
            return "None"
    
    df["time_band"] = df.apply(recode_time_window, axis=1)
    
    # Create competitor indicator
    df['Has_Competitor'] = df['Competitor SKU'].notna().astype(int)
    
    # Create clean dataset (drop missing outcome values)
    df_clean = df.dropna(subset=['ABI vs Segment PTC Index Agg', 'Retailer', 'ABI Mechanic', 'time_band'])
    
    print(f"\n📊 ANALYSIS RESULTS:")
    print(f"Original dataset: {len(df)} records")
    print(f"Clean dataset: {len(df_clean)} records")
    print(f"Records dropped: {len(df) - len(df_clean)}")
    
    # Check time band distribution
    print(f"\n⏰ TIME BAND DISTRIBUTION:")
    print("Full dataset:")
    time_full = df['time_band'].value_counts().sort_index()
    print(time_full)
    
    print("\nClean dataset:")
    time_clean = df_clean['time_band'].value_counts().sort_index()
    print(time_clean)
    
    # Check competitor presence
    print(f"\n🏢 COMPETITOR PRESENCE:")
    print("Full dataset:")
    comp_full = df['Has_Competitor'].value_counts().sort_index()
    print(f"No Competitor (0): {comp_full.get(0, 0)}")
    print(f"Has Competitor (1): {comp_full.get(1, 0)}")
    
    print("\nClean dataset:")
    comp_clean = df_clean['Has_Competitor'].value_counts().sort_index()
    print(f"No Competitor (0): {comp_clean.get(0, 0)}")
    print(f"Has Competitor (1): {comp_clean.get(1, 0)}")
    
    # Calculate percentages for competitor presence
    total_clean = len(df_clean)
    no_comp = comp_clean.get(0, 0)
    has_comp = comp_clean.get(1, 0)
    
    print(f"\nPercentages (clean dataset):")
    print(f"No Competitor: {no_comp/total_clean*100:.1f}%")
    print(f"Has Competitor: {has_comp/total_clean*100:.1f}%")
    
    # Identify the issues
    print(f"\n🚨 IDENTIFIED ISSUES:")
    
    # Issue 1: Check if value_counts() order matches labels
    comp_counts_desc = df_clean['Has_Competitor'].value_counts()  # Default descending order
    print(f"\nIssue 1 - Competitor Presence Labels:")
    print(f"value_counts() returns: {comp_counts_desc.index.tolist()} with values {comp_counts_desc.values.tolist()}")
    print(f"But labels in code are: ['No Competitor', 'Has Competitor']")
    print(f"❌ MISMATCH: Labels are in wrong order!")
    
    # Issue 2: Check time band numbers
    print(f"\nIssue 2 - Time Band Numbers:")
    print(f"Your chart shows: None=228, Before=208, After=182, Same=71")
    print(f"Clean data shows: {dict(time_clean)}")
    print(f"Full data shows: {dict(time_full)}")
    
    if dict(time_clean) != {'After': 182, 'Before': 208, 'None': 228, 'Same': 71}:
        print(f"❌ MISMATCH: Time band numbers don't match your chart!")
    else:
        print(f"✅ Time band numbers match")
    
    print(f"\n🔧 RECOMMENDED FIXES:")
    print(f"1. Fix competitor presence labels - they should be in reverse order")
    print(f"2. Verify which dataset (full vs clean) should be used for charts")
    print(f"3. Check if there are any data filtering steps missing")

if __name__ == "__main__":
    main()
