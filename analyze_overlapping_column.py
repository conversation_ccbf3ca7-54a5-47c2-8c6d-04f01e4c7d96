#!/usr/bin/env python3
"""
Analyze the 'Overlapping' column and create an improved time band classification
that combines Same Week and Overlapping promotions.
"""

import pandas as pd
import numpy as np

def analyze_overlapping_column():
    print("🔍 Analyzing the 'Overlapping' Column...")
    
    # Load and process data
    df = pd.read_csv("greater than 10 new_test_output_ads_v3.csv", index_col=0)
    
    # Check the Overlapping column
    print(f"📊 OVERLAPPING COLUMN ANALYSIS:")
    print(f"Total records: {len(df)}")
    
    # Analyze overlapping column
    overlapping_counts = df['Overlapping'].value_counts(dropna=False)
    print(f"\nOverlapping column values:")
    for value, count in overlapping_counts.items():
        print(f"  {value}: {count} ({count/len(df)*100:.1f}%)")
    
    # Convert timing columns to numeric
    timing_cols = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']
    for col in timing_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Convert overlapping to numeric if needed
    df['Overlapping_numeric'] = pd.to_numeric(df['Overlapping'], errors='coerce')
    
    print(f"\nOverlapping column (numeric) values:")
    overlapping_numeric_counts = df['Overlapping_numeric'].value_counts(dropna=False)
    for value, count in overlapping_numeric_counts.items():
        print(f"  {value}: {count} ({count/len(df)*100:.1f}%)")
    
    # Analyze relationship between Same Week and Overlapping
    print(f"\n🔍 SAME WEEK vs OVERLAPPING RELATIONSHIP:")
    
    # Create cross-tabulation
    same_week_numeric = pd.to_numeric(df['Same Week'], errors='coerce')
    crosstab = pd.crosstab(same_week_numeric, df['Overlapping_numeric'], 
                          margins=True, dropna=False)
    print(f"\nCross-tabulation (Same Week vs Overlapping):")
    print(crosstab)
    
    # Analyze records where both Same Week and Overlapping are active
    both_active = df[(same_week_numeric == 1) & (df['Overlapping_numeric'] == 1)]
    print(f"\nRecords with both Same Week=1 AND Overlapping=1: {len(both_active)}")
    
    # Analyze records where either Same Week OR Overlapping is active
    either_active = df[(same_week_numeric == 1) | (df['Overlapping_numeric'] == 1)]
    print(f"Records with Same Week=1 OR Overlapping=1: {len(either_active)}")
    
    # Create improved time band classification
    print(f"\n🆕 IMPROVED TIME BAND CLASSIFICATION:")
    
    def classify_improved_time_band(row):
        same_week = 1 if pd.to_numeric(row['Same Week'], errors='coerce') == 1 else 0
        overlapping = 1 if pd.to_numeric(row['Overlapping'], errors='coerce') == 1 else 0
        before_1 = 1 if row['1 wk before'] == 1 else 0
        before_2 = 1 if row['2 wk before'] == 1 else 0
        after_1 = 1 if row['1 wk after'] == 1 else 0
        after_2 = 1 if row['2 wk after'] == 1 else 0
        
        # Priority: Concurrent (Same Week OR Overlapping) > Before > After > None
        if same_week == 1 or overlapping == 1:
            return "Concurrent"
        elif before_1 == 1 or before_2 == 1:
            return "Before"
        elif after_1 == 1 or after_2 == 1:
            return "After"
        else:
            return "None"
    
    df['improved_time_band'] = df.apply(classify_improved_time_band, axis=1)
    improved_counts = df['improved_time_band'].value_counts()
    
    print("Improved time band distribution:")
    for band, count in improved_counts.items():
        print(f"  {band}: {count} ({count/len(df)*100:.1f}%)")
    
    # Compare with current approach
    def classify_current_time_band(row):
        same_week = 1 if pd.to_numeric(row['Same Week'], errors='coerce') == 1 else 0
        
        if same_week == 1:
            return "Same"
        elif row['1 wk before'] == 1 or row['2 wk before'] == 1:
            return "Before"
        elif row['1 wk after'] == 1 or row['2 wk after'] == 1:
            return "After"
        else:
            return "None"
    
    df['current_time_band'] = df.apply(classify_current_time_band, axis=1)
    current_counts = df['current_time_band'].value_counts()
    
    print(f"\n📈 COMPARISON:")
    comparison_df = pd.DataFrame({
        'Current_Approach': current_counts,
        'Improved_Approach': improved_counts
    }).fillna(0).astype(int)
    
    print(comparison_df)
    
    # Analyze what changes
    changes = df[df['current_time_band'] != df['improved_time_band']]
    print(f"\n🔄 CHANGES FROM CURRENT TO IMPROVED APPROACH:")
    print(f"Total records changing: {len(changes)}")
    
    if len(changes) > 0:
        change_summary = changes.groupby(['current_time_band', 'improved_time_band']).size()
        for (old, new), count in change_summary.items():
            print(f"  {old} -> {new}: {count} records")
    
    # Analyze the specific records that change
    print(f"\n📋 SAMPLE RECORDS THAT CHANGE CLASSIFICATION:")
    if len(changes) > 0:
        sample_changes = changes[['Same Week', 'Overlapping', '1 wk before', '2 wk before', 
                                '1 wk after', '2 wk after', 'current_time_band', 
                                'improved_time_band']].head(10)
        
        for idx, (_, row) in enumerate(sample_changes.iterrows()):
            print(f"  {idx+1}. SW={row['Same Week']}, OL={row['Overlapping']}, "
                  f"1B={row['1 wk before']}, 2B={row['2 wk before']}, "
                  f"1A={row['1 wk after']}, 2A={row['2 wk after']} "
                  f"| {row['current_time_band']} -> {row['improved_time_band']}")
    
    # Business impact analysis
    print(f"\n💼 BUSINESS IMPACT ANALYSIS:")
    
    concurrent_gain = improved_counts.get('Concurrent', 0) - current_counts.get('Same', 0)
    none_reduction = current_counts.get('None', 0) - improved_counts.get('None', 0)
    
    print(f"1. CONCURRENT COMPETITIVE PRESSURE:")
    print(f"   - Current approach: {current_counts.get('Same', 0)} records (Same Week only)")
    print(f"   - Improved approach: {improved_counts.get('Concurrent', 0)} records (Same Week + Overlapping)")
    print(f"   - Gain: +{concurrent_gain} records ({concurrent_gain/len(df)*100:.1f}%)")
    
    print(f"\n2. NO COMPETITIVE PRESSURE:")
    print(f"   - Current approach: {current_counts.get('None', 0)} records")
    print(f"   - Improved approach: {improved_counts.get('None', 0)} records")
    print(f"   - Reduction: -{none_reduction} records ({none_reduction/len(df)*100:.1f}%)")
    
    print(f"\n3. STRATEGIC IMPLICATIONS:")
    print(f"   ✅ More accurate identification of competitive pressure")
    print(f"   ✅ Better understanding of promotional timing dynamics")
    print(f"   ✅ Improved segmentation for promotional effectiveness analysis")
    print(f"   ✅ More actionable insights for promotional planning")
    
    return df

if __name__ == "__main__":
    df = analyze_overlapping_column()
    print(f"\n✅ Analysis complete!")
